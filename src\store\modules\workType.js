import { getSheetTypeList } from '@/api/mySheetManage';
import router from '../../router';
import Layout from '@/views/layout/Layout';
const state = {
  workType: [],
  newRoutes: []
};
const mutations = {
  SET_WORK_TYPE: (state, type) => {
    state.workType = type;
  },
  SET_NEW_ROUTES: (state, routes) => {
    state.newRoutes = routes;
  }
};

const actions = {
  getWorkType({ commit }) {
    return new Promise((resolve, reject) => {
      getSheetTypeList({ status: 1 })
        .then(res => {
          if (res.data && res.data instanceof Array && res.data.length > 0) {
            const newRoutes = getDynanicRoute(res.data);
            commit('SET_NEW_ROUTES', newRoutes);
            commit('SET_WORK_TYPE', res.data);
            let defRoutes;
            if (localStorage.getItem('defRoutes')) {
              defRoutes = JSON.parse(localStorage.getItem('defRoutes'));
            } else {
              defRoutes = router.options.routes;
              localStorage.setItem('defRoutes', JSON.stringify(defRoutes));
            }
            const integralRoutes = defRoutes.concat(newRoutes);
            router.options.routes = integralRoutes;
            router.addRoutes(newRoutes);
            resolve(newRoutes);
          } else {
            reject('没有可用的工单类型');
          }
        })
        .catch(error => {
          reject(error);
        });
    });
  }
};
function getDynanicRoute(routes) {
  const arr = [];
  routes.forEach(item => {
    arr.push({
      path: `/workStatus${item.id}`,
      name: 'workStatus' + item.id,
      redirect: `/workStatus${item.id}/listAllWorkorder`,
      meta: {
        title: item.name,
        affix: true,
        code: 'menu:cs:workorder',
        mark: 'workSheet'
      },
      component: Layout,
      children: [
        {
          path: `listAllWorkorder`,
          name: `listAllWorkorder${item.id}`,
          meta: {
            title: '所有工单',
            code: 'menu:cs:typeallworkorderby',
            mark: 'workSheet',
            query: {
              title: item.name + '-所有工单',
              id: item.id,
              interface: 'listAllWorkorder',
              status: '8',
              queryParameterJson: {}
            }
          },
          component: () => import('@/views/work-sheet/list')
        },
        {
          path: `listUnclaimedWorkorder`,
          name: `listUnclaimedWorkorder${item.id}`,
          meta: {
            title: '待领取',
            code: 'menu:cs:prereceive',
            mark: 'workSheet',
            query: {
              title: item.name + '-待领取',
              id: item.id,
              interface: 'listUnclaimedWorkorder',
              status: '4',
              queryParameterJson: {}
            }
          },
          component: () => import('@/views/work-sheet/list')
        },
        {
          path: `listMyProcessingC1`,
          name: `listMyProcessingC1${item.id}`,
          meta: {
            title: '我处理中的',
            code: 'menu:cs:meprocessing',
            mark: 'workSheet',
            query: {
              title: item.name + '-我处理中的',
              id: item.id,
              interface: 'listMyProcessing',
              status: '3',
              queryParameterJson: { current_state: 1 }
            }
          },
          component: () => import('@/views/work-sheet/list')
        },
        {
          path: `listMyProcessingT1`,
          name: `listMyProcessingT1${item.id}`,
          meta: {
            title: '我处理-已超时',
            code: 'menu:cs:metimeout',
            mark: 'workSheet',
            query: {
              title: item.name + '-我处理-已超时',
              id: item.id,
              interface: 'listMyProcessing',
              status: '1',
              queryParameterJson: { timeout_state: 1 }
            }
          },
          component: () => import('@/views/work-sheet/list')
        },
        {
          path: `listMyProcessingT2`,
          name: `listMyProcessingT2${item.id}`,
          meta: {
            title: '我处理-即将超时',
            code: 'menu:cs:metimingout',
            mark: 'workSheet',
            query: {
              title: item.name + '-我处理-即将超时',
              id: item.id,
              interface: 'listMyProcessing',
              status: '2',
              queryParameterJson: { timeout_state: 2 }
            }
          },
          component: () => import('@/views/work-sheet/list')
        },
        {
          path: `listMyProcessed`,
          name: `listMyProcessed${item.id}`,
          meta: {
            title: '我已处理的',
            code: 'menu:cs:meprocessed',
            mark: 'workSheet',
            query: {
              title: item.name + '-我已处理的',
              id: item.id,
              interface: 'listMyProcessed',
              status: '5',
              queryParameterJson: {}
            }
          },
          component: () => import('@/views/work-sheet/list')
        },
        {
          path: `listIInitiated`,
          name: `listIInitiated${item.id}`,
          meta: {
            title: '我发起的',
            code: 'menu:cs:meinitiate',
            mark: 'workSheet',
            query: {
              title: item.name + '-我发起的',
              id: item.id,
              interface: 'listIInitiated',
              status: '6',
              queryParameterJson: {}
            }
          },
          component: () => import('@/views/work-sheet/list')
        },
        {
          path: `listMyCC`,
          name: `listMyCC${item.id}`,
          meta: {
            title: '抄送给我的',
            code: 'menu:cs:cctome',
            mark: 'workSheet',
            query: {
              title: item.name + '-抄送给我的',
              id: item.id,
              interface: 'listMyCC',
              status: '7',
              queryParameterJson: {}
            }
          },
          component: () => import('@/views/work-sheet/list')
        },
        {
          path:'listMyFollow',
          name:`listMyFollow${item.id}`,
          meta:{
            title:'我关注的',
            code:'menu:cs:listmyfollow',
            mark:'workSheet',
            query:{
              title:item.name + '-我关注的',
              id:item.id,
              interface:'listMyFollow',
              status:'9',
              queryParameterJson:{}
            }
          },
          component: () => import('@/views/work-sheet/list')
        },
        // {
        //   path: `newList/:t/:t`,
        //   name: `worknewListSheetList${item.id}`,
        //   meta: {
        //     title: '……',
        //     code: 'menu:cs:workorder',
        //     mark: 'workSheet',
        //     query: {
        //       id: item.id
        //     }
        //   },
        //   component: () => import('@/views/work-sheet/new-list'),
        //   hidden: true
        // },
        {
          path: `newSheet/:t`,
          name: `newSheet${item.id}`,
          meta: {
            title: '新建工单',
            code: 'menu:cs:workorder',
            mark: 'workSheet',
            query: {
              id: item.id
            }
          },
          component: () => import('@/views/work-sheet/new-sheet'),
          hidden: true
        },
        {
          path: 'sheetDetail/:t',
          name: `sheetDetail${item.id}`,
          meta: {
            title: '工单详情',
            code: 'menu:cs:workorder',
            mark: 'workSheet',
            query: {
              id: item.id
            }
          },
          component: () => import('@/views/work-sheet/sheet-detail'),
          hidden: true
        },

      ]
    });
  });
  arr.unshift({
    path: '/intervention',
    component: Layout,
    name: 'intervention',
    redirect: '/intervention/list',
    meta: {
      title: '平台介入工单',
      icon: 'allworkorder',
      affix: true,
      code: 'menu:cs:alltypeallworkorder',
      mark: 'workSheet'
    },
    children: [
      {
        path: 'list',
        name: 'interventionSheetList',
        meta: {
          title: '平台介入工单',
          code: 'menu:cs:alltypeallworkorder',
          mark: 'workSheet'
        },
        component: () => import('@/views/intervention-tcket/list')
      },
      {
        path: 'intervDetail/:t',
        name: 'interventionDetail',
        meta: {
          title: '工单详情',
          code: 'menu:cs:workorder',
          mark: 'workSheet'
        },
        component: () => import('@/views/intervention-tcket/interv-detail'),
        hidden: true
      },
    ]
  });
  arr.unshift({
    path: '/allSheet',
    component: Layout,
    name: 'allSheet',
    redirect: '/allSheet/list',
    meta: {
      title: '所有工单',
      icon: 'allworkorder',
      affix: true,
      code: 'menu:cs:alltypeallworkorder',
      mark: 'workSheet'
    },
    children: [
      {
        path: 'list',
        name: 'allSheetList',
        meta: {
          title: '所有工单',
          code: 'menu:cs:alltypeallworkorder',
          mark: 'workSheet'
        },
        component: () => import('@/views/all-sheet/list')
      }
    ]
  });
  return arr;
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
};
