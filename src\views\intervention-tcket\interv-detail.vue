<template>
  <div>
    <div class="page">
      <el-row type="flex" class="sheeTitle" justify="space-between" align="middle">
        <span>
          <span
            v-show="baseWorkorderInfo.form_type_name && baseWorkorderInfo.workorder_num"
          >{{ baseWorkorderInfo.form_type_name }}-{{ baseWorkorderInfo.workorder_num }}</span>
          <i>{{ baseWorkorderInfo.current_state | statusFormat }}</i>
        </span>
        <span v-if="tags.length" class="order-tag">
          当前标签：
          <el-popover placement="right" width="400" trigger="click">
            <el-button
              v-if="orderTag.tagId"
              size="mini"
              class="tag-btn"
              type="danger"
              icon="el-icon-delete"
              @click="clearTag()"
            >清除标签</el-button>
            <el-button
              v-for="(tag,key) in tags.filter(item=>item.id!=orderTag.tagId)"
              :key="key"
              size="mini"
              class="tag-btn"
              @click="switchTag(tag)"
            >{{ tag.tagName }}</el-button>
            <el-button
              v-if="!orderTag.tagId"
              slot="reference"
              :disabled="isDisabledTag"
              type="info"
              size="mini"
              class="tag-btn"
              icon="el-icon-plus"
              circle
            ></el-button>
            <el-button
              v-if="orderTag.tagId"
              slot="reference"
              :disabled="isDisabledTag"
              size="mini"
              class="tag-btn"
            >{{ orderTag.tagName }}</el-button>
          </el-popover>
        </span>
        <el-row type="flex">
          <xyy-button class="el-icon-refresh" type="normal" @click="mountedHandle">刷新</xyy-button>
        </el-row>
      </el-row>
      <el-divider></el-divider>
      <el-row type="flex" justify="start" align="top">
        <div class="detailInstr">
          <!-- <open-info
            :clear-empty="true"
            :data-info="extendWorkorderFieldinfo"
            :lay-datas="layDatas"
            :info="true"></open-info> -->
            <!-- 客户信息展示区域 -->
          <div class="customer-info">
            <div class="info-row">
              <span class="info-label">客户名称：</span>
              <span class="info-value">金融港大药房</span>
            </div>
            <div class="info-row">
              <span class="info-label">客户ID：</span>
              <span class="info-value">1234567890</span>
            </div>
            <div class="info-row">
              <span class="info-label">介入类型：</span>
              <span class="info-value">商品问题</span>
            </div>
            <div class="info-row">
              <span class="info-label">商品问题：</span>
              <span class="info-value">包装破损</span>
            </div>
            <div class="info-row">
              <span class="info-label">客户诉求：</span>
              <span class="info-value">退货退款</span>
            </div>
            <div class="info-row">
              <span class="info-label">联系方式：</span>
              <span class="info-value">155 1234 5678</span>
            </div>
            <div class="info-row">
              <span class="info-label">客户邮箱：</span>
              <span class="info-value"><EMAIL></span>
            </div>
            <div class="info-row">
              <span class="info-label">订单号：</span>
              <span class="info-value">YBM20250519175638100041 <el-button type="text" size="mini" class="copy-btn">复制</el-button></span>
            </div>
            <div class="info-row">
              <span class="info-label">退款单号：</span>
              <span class="info-value">RS20250519175638100041 <el-button type="text" size="mini" class="copy-btn">复制</el-button></span>
            </div>
            <div class="info-row">
              <span class="info-label">买家名称：</span>
              <span class="info-value">湖北小药药自营旗舰店</span>
            </div>
          </div>
          <el-divider></el-divider>
          <!-- 客户申请平台介入商品 -->
          <div class="product-section">
            <h3 class="section-title">客户申请平台介入商品</h3>
            <div class="product-grid">
              <!-- 商品列表 -->
              <div class="product-item" v-for="(product, index) in productList" :key="index">
                <div class="product-image">
                  <img :src="product.image" :alt="product.name" />
                  <div class="product-quantity">×{{ product.quantity }}</div>
                </div>
                <div class="product-info">
                  <div class="product-name">{{ product.name }}</div>
                  <div class="product-specs">{{ product.specs }}</div>
                </div>
              </div>
            </div>

            <!-- 共6种商品 -->
            <div class="product-summary">
              <span class="summary-text">共6种商品</span>
            </div>
          </div>

          <!-- 退款信息 -->
          <div class="refund-info">
            <div class="info-row">
              <span class="info-label">退款金额：</span>
              <span class="info-value refund-amount">¥255.15</span>
            </div>
            <div class="info-row">
              <span class="info-label">退款原因：</span>
              <span class="info-value">退货退款</span>
            </div>
            <div class="info-row">
              <span class="info-label">申请时间：</span>
              <span class="info-value">2025-05-19 17:56:37</span>
            </div>
          </div>
        </div>
        <!-- v-if="checkPermission('btn:cs:workorderedit')" -->
        <el-divider direction="vertical"></el-divider>
        <el-col class="detailShow">
          <processs
            ref="processs"
            :id="workorderId"
            :attr="baseWorkorderInfo"
            @publishCallback="issueAppendHid = true"
          ></processs>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
// /workorder/operation/startAgai
import {
  deleteTag,
  getOrderDetailTag,
  getTagList,
  putSwitchTag,
} from '@/api/mySheetManage';
import filePreview from '@/components/Fields/file-preview';
import openInfo from './components/openInfo';
import processs from './components/process';
import pubUrl from './mixin/pubUrl';

export default {
  name: 'interventionDetail',
  components: {
    filePreview,
    openInfo,
    processs
  },
  filters: {
    statusFormat(val) {
      let name = '';
      switch (val) {
        case 0:
          name = '待领取';
          break;
        case 1:
          name = '处理中';
          break;
        case 2:
          name = '完结';
          break;
        case 3:
          name = '已退回';
          break;
        case 4:
          name = '异常';
          break;
        case 5:
          name = '作废';
          break;
      }
      return name;
    }
  },
  mixins: [pubUrl],
  data() {
    return {
      layDatas: '', // 布局数据
      replyLayDatas: '', 
      urlAction: process.env.BASE_API + '/fileUpload/uploadFile',
      fileNums: 10, // 文件最大上传数
      uploadList: [], // 上传队列
      failureList: [], // 上传失败文件列表
      uploadingList: false, // 文件上传中
      previewModal: [], // 附件上传返回值的数组
      previewModalarr: [], // 向后台传的数据
      update_problem_classification: false, // 切换问题分类为true
      locationStatus: false, // 客户所在地校验弹框
      locationMsg: '', // 客户所在地校验信息
      assRadioSign: 'people', // 工单转移
      assignmentDialog: false, // 工单转移
      assRuleForm: {
        selNodeVal: '', // 选择节点值
        userGroupVal: '',
        memberVal: ''
      },
      currentNodeInfo: [],
      currentNodeGroupInfo: [], //
      memberArray: [],
      assRules: {
        selNodeVal: [
          { required: true, message: '请选择节点名称', trigger: 'blur' }
        ],
        userGroupVal: [
          { required: true, message: '请选择用户组', trigger: 'blur' }
        ],
        memberVal: [
          { required: true, message: '请选择组内成员', trigger: 'blur' }
        ]
      },
      urgeWorkHid: false,
      currentProcessPersonName: '',
      currentNodeName: '',
      urgeWorkCon: '',
      textarea: '',
      editfromData: [], // 编辑表单
      sheetPre: [],
      extendWorkorderField: [], // 编辑工单发起字段对象
      record: 1, // 1.流转 2.催单
      editForm: false,
      sheetResetShow: false,
      returnSheetHid: false, // 退回工单
      returnSheetRadio: '0', // 1:退回上一环节,0:退回发起人
      returnSheetCon: '', // 退回工单内容
      returnSheetTip: {
        '1': '提示：退单后，工单将退回给上一环节处理人',
        '0': '提示：退单后，工单将退回给发起人，工单流程终止'
      }, // 退回工单提示
      baseWorkorderInfo: {
      },
      issueAppendHid: false,
      appendNotesCon: '',
      extendWorkorderFieldinfo: [],
      isCloseNode: false,
      workorderId: '',
      assignForm: {
        assignNode: '',
        assignUsergroup: '',
        assignMembers: ''
      },
      assignRules: {
        assignNode: [
          { required: true, message: '请选择节点名称', trigger: 'change' }
        ]
      },
      assignNodes: [], // 转移节点数据
      assignGroups: [], // 转移节点用户组数据
      assignUsers: [], // 转移节点用户数据
      restartForm: {
        restartReason: ''
      },
      againRestart: false,
      workorderNum: '',
      systemArr: [],
      customeArr: [],
      orderTag: {
        tagId: '',
        tagName: ''
      },
      tags: [],
      searchResult: [], // 根据手机号搜索出来的店铺信息
      tempMerchantInfo: {}, // 根据客户电话匹配出来的客户信息

      // 重复工单
      repeatWorksheet: {
        workorderId: '',
        workorderNum: '',
        addSheetCheckHid: false
      }
    };
  },
  computed: {
    currentState() {
      return this.baseWorkorderInfo.current_state;
    },
    isDisabledTag() {
      return !this.$store.getters.validBtn.some(item => {
        return item.code === 'btn:cs:workordertag';
      });
    }
  },
  watch: {
    editfromData: function(val) {
      this.systemArr.splice(0, this.systemArr.length);
      this.customeArr.splice(0, this.customeArr.length);
      val.forEach(item => {
        if (item.systemFieldFlag === 0) {
          this.systemArr.push(item);
        } else {
          this.customeArr.push(item);
        }
      });
    }
  },
  mounted: function() {
    this.mountedHandle(); // 页面初始话及请求
  },
  methods: {
    goDetail(id) {
      this.repeatWorksheet.addSheetCheckHid = false;
      this.editForm = false;
      this.$router.push({
        path: '/workStatus' + this.baseWorkorderInfo.form_type_id + '/sheetDetail/' + id,
        query: { id: id, type: 'newSheet' }
      });
    },
    // 附件上传之前拦截
    beforeUpload(file) {
      this.uploadingList = false;
      this.uploadList = [].concat(file);
      if (file.size / 1024 / 1000 > 500) {
        // 附件上传大小的限制
        this.$XyyMessage.error(`附件上传大小不能超过500M`);
        return false;
      }

      if (file.type) {
        if (!MIME_TYPES.includes(file.type)) {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png pdf zip rar mp4 avi doc docx xls xlsx ppt pptx txt</span>`, // html代码串
            onSuccess: () => {
            }
          });
          return false;
        }
      } else {
        // element ui 插件bug 无法检测rar rar文件以后缀方式判断
        const _type = file.name
          .slice(file.name.lastIndexOf('.') + 1)
          .toLowerCase();
        if (_type !== 'rar') {
          this.$XyyMsg({
            title: '提示',
            closeBtn: false,
            content: `上传文件格式仅限：<span style="color:red;">gif bmp jpg jpeg png pdf zip rar mp4 avi doc docx xls xlsx ppt pptx txt</span>`, // html代码串
            onSuccess: () => {
            }
          });
          return false;
        }
      }
    },
    /**
     * 文件个数超出回调
     */
    validateNums(file, fileList) {
      this.$XyyMessage.error(`最多上传${this.fileNums}个`);
    },
    /**
     * 上传中回调
     */
    handleProgress(event, file) {
      if (file) {
        this.uploadingList = true;
        file.percent = Number(event.percent.toFixed(0));
        if (file.percent > 99) file.percent = 99;
      }
    },

    //    上传成功的回调
    handleSuccess(res, file) {
      if (res.code === 1) {
        if (!res.data.failFiles.length) {
          file.percent = 100;
          const _file = {
            uid: file.uid,
            name: file.name,
            url:
              res.data.baseUrl +
              (res.data.successFiles[0].thumbImagePath ||
                res.data.successFiles[0].group +
                '/' +
                res.data.successFiles[0].path), // 预览路径
            path:
              res.data.baseUrl +
              res.data.successFiles[0].group +
              '/' +
              res.data.successFiles[0].path, // 真实路径
            percent: 100,
            raw: {
              type: file.raw.type
                ? file.raw.type
                : 'application/x-rar-compressed',
              size: file.raw.size
            },
            data: res.data.successFiles[0]
          };
          this.previewModal.push(_file);
        } else {
          this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
            if (el.uid === file.uid) {
              const _file = this.$refs['myUploader'].uploadFiles.splice(i, 1);
              this.failureList = this.failureList.concat(_file);
            }
            this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
          });

          let msg = '';
          if (this.failureList.length > 1) {
            msg = `${this.failureList[0].name}等${this.failureList.length}个文件上传失败`;
          } else {
            msg = `${this.failureList[0].name}上传失败`;
          }
          this.$XyyMessage.error(msg);
        }
      } else {
        this.$XyyMessage.error(res.msg);
      }
      this.uploadingList = false;
    },
    //    上传失败的回调
    handleError(res) {
      this.uploadingList = false;
      this.$XyyMessage.error(`文件上传失败`);
    },
    /**
     * 上传点击事件 初始化上传失败数组
     */
    uploadStart() {
      this.failureList = [];
    },
    /**
     * 取消上传
     */
    abortFile(file) {
      this.$refs['myUploader'].abort(file);
      this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['myUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
      this.uploadingList = false;
    },

    // 附件删除
    delFile(file) {
      // 删除上传队列中的数据
      this.$refs['myUploader'].uploadFiles.forEach((el, i) => {
        if (el.uid === file.uid) {
          this.$refs['myUploader'].uploadFiles.splice(i, 1);
        }
      });
      this.uploadList = [].concat(this.$refs['myUploader'].uploadFiles);
      // 删除存储的数据
      this.previewModal.forEach((item, index) => {
        if (file.uid === item.uid) {
          this.previewModal.splice(index, 1);
        }
      });
    },
    downFile(name, filePath) {
      const preParam = { fieldName: name, fieldPath: filePath };

      function httpPost(URL, PARAMS) {
        var temp = document.createElement('form');
        temp.action = URL;
        temp.method = 'get';
        temp.style.display = 'none';

        for (var x in PARAMS) {
          var opt = document.createElement('textarea');
          opt.name = x;
          opt.value = PARAMS[x];
          temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
      }

      let domain = document.domain;
      if (domain === 'localhost') {
        domain = 'ec-service.dev.ybm100.com';
      }
      httpPost('http://' + domain + '/fileUpload/fileDownload', preParam);
    },
    // 查询工单标签
    getOrderTag(id) {
      getOrderDetailTag(id).then(res => {
        this.orderTag = { ...res.data };
      });
    },
    // 获取标签字典
    getTags() {
      getTagList().then(res => {
        this.tags = res.data;
      });
    },
    // 切换标签
    switchTag(tag) {
      putSwitchTag(tag.id, this.workorderId).then(res => {
        if (res.code === 1) {
          this.orderTag = {
            tagId: tag.id,
            tagName: tag.tagName
          };
        }
      });
    },
    // 清除标签
    clearTag() {
      deleteTag(this.workorderId).then(res => {
        if (res.code === 1) {
          this.orderTag = {
            tagId: '',
            tagName: ''
          };
        }
      });
    },
    /**
     * 页面初始加载
     */
    mountedHandle() {
      Object.assign(this.$data, this.$options.data()); // 重置data数据
      const { id, type } = this.$route.query;
      this.pageType = type;
      this.workorderId = id;
      const orderParam = { workorderId: id };
      this.$refs['processs'].getRecordDatas(); // 获取流转记录-催单记录-追加记录
    },
    /**
     * 更新数据后store增加上一级列表url，以便打开列表更新数据
     */
    addModifyFlag(refs) {
      if (this.$route.query.currentUrl) {
        this.$store.commit(
          'workSheet/SET_MODIFY_FLAG',
          this.$route.query.currentUrl
        );
      }
      if (!refs) this.mountedHandle();
    }
  }
};
</script>
<style lang="scss">
.detailInstr[data-v-0f2e067e] {
    position: relative;
    padding: 30px 10px 20px 20px;
    font-size: 14px;
    font-weight: 400;
    width: 55%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
</style>


