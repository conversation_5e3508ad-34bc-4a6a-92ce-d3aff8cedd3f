'use strict';
// Template version: 1.2.6
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path');
const ip = require('ip').address();

module.exports = {
  dev: {
    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '*': {
        // target: 'http://***************:8019/', // 史玉杰
        // target: 'http://**************:8019/', // 李园林
        // target: 'http://ec-service.dev.ybm100.com',
        target: 'https://ec-service.test.ybm100.com/', // 开发
        changeOrigin: true, // 如果接口跨域，需要进行这个参数配置
        secure: true, // 如果是https接口，需要配置这个参数，并配置headers
        onProxyReq(proxyReq, req) {
          const { cookie } = req.headers;
          if (cookie) {
            proxyReq.setHeader('Origin', 'https://ec-service.test.ybm100.com/');
            proxyReq.setHeader(
              'Cookie',
              'crosSdkDT2019DeviceId=-aaq91e--p7z7kn-8t6jdqkoch953pf-3tysxopz1; web_dc504156a0b54a04bd19c57da5422a32=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFio%5C%22%7D%22%7D; _abfpc=1286e3be76f5587a48f4cc11dd0e4ebe100b5e91_2.0; cna=b479add222b191174aed70a4b95b6a80; ssxmod_itna=eqfO7KGKBI1GCDBD4q4jx7FnIKGRtDKq0dGMlDeq7tDRDFqAPDODCg0I1C7hgrtxDQ5Yj0RGrrzAqDsPKxiNDAxq0iDC8ejerDfuR4GKGnW9+55dC0RR=o8biZb05F4j/Mqr7kh0A/DU4GnD06qmOmeKxGGj4GwDGoD34DiDDPDb8iDAuqD7qDFIQTtMjbDm4GWIeDmDGAokeDgIDDB+30iWOb4mxYdx4DlD87GG7DdW6cDwcwinlGF57Gox0taDBd4U9H82FkG46aApKcp8eGyt5GuSnqr5Qe8jCWNr1msFSvrSbIYlLEqQGmeecmYKiD/7hqjGMam5CI/xepCmqAwZ=4DDptSTHSb5j0YiyS8yM+5bn2q8+kAuDmr1luDD47mOQ5rqnxNQwQj0qe4ibGbY0qg04mxXUAQzGDD; ssxmod_itna2=eqfO7KGKBI1GCDBD4q4jx7FnIKGRtDKq0dGMlDeq7tDRDFqAPDODCg0I1C7hgrtxDQ5Yj0RGrrzQ4DWwtC72AKqDjRDOWsUDDs0UIZDce5S+PF+je9+GSQdO5n1Gr2DE3dogODwYzz95mw=O/MDOFkDnlS=Oe+GYFwDubCx0+Wre4ME60ff4+d29mg=PBj6xe3LqvgnqSkTXfRdGYDjrD8mC21HE+8vzKZxhD3xpBZ7xD; web_did=%7B%22did%22%3A%20%22418c6b00-18ff-492d-8c67-44f0ebc8aceb%22%7D; web_cdaeb9bc6854423b9ab74b1827bc21a0=%7B%22sid%22%3A%200%2C%22updated%22%3A%200%2C%22info%22%3A%200%2C%22superProperty%22%3A%20%22%7B%5C%22%E5%BA%94%E7%94%A8%E5%90%8D%E7%A7%B0%5C%22%3A%20%5C%22%E5%B0%8F%E8%8D%AF%E8%8D%AFIM%E5%B7%A5%E5%8D%95%E7%B3%BB%E7%BB%9F%5C%22%7D%22%7D; uid=rBQRYGg/qFWuhhbtPHspAg==; TGC=eyJhbGciOiJIUzUxMiJ9.ZXlKNmFYQWlPaUpFUlVZaUxDSmhiR2NpT2lKa2FYSWlMQ0psYm1NaU9pSkJNVEk0UTBKRExVaFRNalUySW4wLi5oTXR3bFBWTlp3WWxUbVdrcl84SDZBLi1ianlYWGFJNUtzQm1GOXBxZVhRZVBOeUl2OVBuQ2VEMDVGMVJrUklxREpXUDBNN01kSDl2UFpUV0RBNmNmSU41M2V5Q3Q3bWNoVjZqSm1NV3JrZ21PRzlUVUNwOFl3RExPdXpybnNxTk1zY2RaNVBmWUJNSUZIc1RxWi1rUGdldUtJcXRwVzg5SFprZlVxUU44TWtDWmNNV2FtdGloN3BSWXdoUk9wRVRKdV84d0pwVjhFeE5semFGRGZmcm1ibEx3NDFLZVd2TzZRX21NbDdPZ3JiNHRjOVcyUW5hcmZ2ZTgxd2RBd2l1WHk5N3EwMzliRmpDWE5ma1pLbjJNNVJJZUFKSnpWUlJxQ29MQkpsTjVJbWFnLmM4UHdsN2Q5NDFLemIxSnh5VEtUMEE=.70rcYY5ULnGF9vyufnb895n2xyjyg3W8vd6Gt3yjVCgnSlzjaqH-KijzTVSg-CrWUQBEecRNm5RUFipzNbltMg; sid=4562f7f8-1e61-4c56-8910-8b26873427e0; isDeviceUpload=1'
            );
          }
        }
      }
    },
    // Various Dev Server settings
    //host: process.env.HOST, // can be overwritten by process.env.HOST
    host: '127.0.0.1',
    port: 8088, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: true,
    errorOverlay: true,
    notifyOnErrors: false,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    // Use Eslint Loader?
    // If true, your code will be linted during bundling and
    // linting errors and warnings will be shown in the console.
    useEslint: true,
    // If true, eslint errors and warnings will also be shown in the error overlay
    // in the browser.
    showEslintErrorsInOverlay: false,

    /**
     * Source Maps
     */
    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'cheap-source-map',

    // CSS Sourcemaps off by default because relative paths are "buggy"
    // with this option, according to the CSS-Loader README
    // (https://github.com/webpack/css-loader#sourcemaps)
    // In our experience, they generally work as expected,
    // just be aware of this issue when enabling this option.
    cssSourceMap: false
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist/index.html'),

    // Paths
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',

    /**
     * You can set by youself according to actual condition
     * You will need to set this if you plan to deploy your site under a sub path,
     * for example GitHub pages. If you plan to deploy your site to https://foo.github.io/bar/,
     * then assetsPublicPath should be set to "/bar/".
     * In most cases please use '/' !!!
     */
    assetsPublicPath: './',

    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: 'source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report || false,

    // `npm run build:prod --generate_report`
    generateAnalyzerReport: process.env.npm_config_generate_report || false
  }
};
